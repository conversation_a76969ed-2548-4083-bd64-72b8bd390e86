import { motion } from 'framer-motion';

import { Link } from 'react-router-dom';
import { SiBandcamp, SiYoutube, SiInstagram, SiSoundcloud } from 'react-icons/si';
import { Button } from '@/components/ui/button';
import { UnifiedBackground } from '@/components/UnifiedBackground';
import { useScrollReveal, scrollRevealVariants } from '@/hooks/useScrollReveal';

// Removed - now handled by BackgroundAnimation component

export const LandingPage = () => {
  const { ref: heroRef, isVisible: heroVisible } = useScrollReveal({ threshold: 0.2 });
  const { ref: buttonsRef, isVisible: buttonsVisible } = useScrollReveal({ threshold: 0.3 });
  const { ref: socialRef, isVisible: socialVisible } = useScrollReveal({ threshold: 0.4 });

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Unified Background with collage animation and interactive particles */}
      <UnifiedBackground variant="collage" enableInteractiveParticles={true} className="z-0" />

      {/* Translucent Overlay for Content Readability */}
      <div className="absolute inset-0 z-[5] bg-gradient-to-b from-auspex-navy-950/10 via-auspex-navy-900/20 to-auspex-navy-950/30" />

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={heroRef}
          className="text-center max-w-6xl mx-auto"
          variants={scrollRevealVariants.staggerContainer}
          initial="hidden"
          animate={heroVisible ? 'visible' : 'hidden'}
        >
          {/* Logo */}
          <motion.div variants={scrollRevealVariants.scaleIn} className="mb-8">
            <img
              src="/assets/logos/White_transparent_background.png"
              alt="Auspex Records"
              className="h-20 md:h-28 lg:h-32 w-auto filter drop-shadow-2xl mx-auto"
              style={{
                filter: 'brightness(1.2) contrast(1.1) drop-shadow(0 0 30px rgba(255, 79, 0, 0.5))',
              }}
            />
          </motion.div>

          {/* Title */}
          <motion.h1
            variants={scrollRevealVariants.staggerItem}
            className="text-5xl md:text-7xl lg:text-8xl font-heading font-bold mb-6 text-gradient animate-gradient"
          >
            AUSPEX RECORDS
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            variants={scrollRevealVariants.staggerItem}
            className="text-2xl md:text-3xl lg:text-4xl text-white mb-8 max-w-4xl leading-relaxed font-body text-center mx-auto"
          >
            Independent label committed to curating and cultivating future-facing sound.
          </motion.p>

          {/* Call to Action Buttons */}
          <motion.div
            ref={buttonsRef}
            variants={scrollRevealVariants.staggerContainer}
            initial="hidden"
            animate={buttonsVisible ? 'visible' : 'hidden'}
            className="flex flex-col sm:flex-row gap-4 mb-8 justify-center"
          >
            <motion.div variants={scrollRevealVariants.staggerItem}>
              <Link to="/releases">
                <Button
                  size="lg"
                  className="liquid-glass px-10 py-5 text-xl font-semibold bg-gradient-primary hover:bg-gradient-secondary border-0 shadow-glow hover:shadow-glow-strong transition-all duration-300 rounded-full"
                >
                  Explore Releases
                </Button>
              </Link>
            </motion.div>

            <motion.div variants={scrollRevealVariants.staggerItem}>
              <Link to="/live">
                <Button
                  variant="outline"
                  size="lg"
                  className="liquid-glass px-10 py-5 text-xl font-semibold text-white border-auspex-navy-400/50 hover:border-auspex-orange-400/50 hover:text-auspex-orange-400 transition-all duration-300 rounded-full"
                >
                  Watch Performances
                </Button>
              </Link>
            </motion.div>
          </motion.div>

          {/* Social Media Icons */}
          <motion.div
            ref={socialRef}
            variants={scrollRevealVariants.staggerContainer}
            initial="hidden"
            animate={socialVisible ? 'visible' : 'hidden'}
            className="flex justify-center gap-6 mb-12"
          >
            {[
              { name: 'Bandcamp', url: 'https://auspexrecords.bandcamp.com', icon: SiBandcamp },
              { name: 'YouTube', url: 'https://www.youtube.com/@AuspexRecords', icon: SiYoutube },
              {
                name: 'Instagram',
                url: 'https://www.instagram.com/auspex_records',
                icon: SiInstagram,
              },
              {
                name: 'SoundCloud',
                url: 'https://soundcloud.com/auspexrecords',
                icon: SiSoundcloud,
              },
            ].map(link => (
              <motion.a
                key={link.name}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                variants={scrollRevealVariants.staggerItem}
                className="ultra-glass p-5 rounded-full text-gray-300 hover:text-auspex-orange-500 transition-all duration-300 border border-auspex-navy-400/30 hover:border-auspex-orange-400/50 cursor-pointer"
                whileHover={{ scale: 1.15, y: -3, rotate: 5 }}
                whileTap={{ scale: 0.95 }}
                title={link.name}
              >
                <link.icon className="text-2xl" />
              </motion.a>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};
