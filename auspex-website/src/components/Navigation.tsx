import { motion } from 'framer-motion';
import { Link, useLocation } from 'react-router-dom';
import { useState, useEffect } from 'react';

export const Navigation = () => {
  const location = useLocation();
  const [isScrolled, setIsScrolled] = useState(false);
  const isActive = (path: string) => location.pathname === path;

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navVariants = {
    initial: { y: -100, opacity: 0 },
    animate: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut' as const,
      },
    },
  };

  return (
    <motion.nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? 'backdrop-blur-glass shadow-glow' : 'bg-transparent'
      }`}
      variants={navVariants}
      initial="initial"
      animate="animate"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-center items-center h-20">
          <nav className="flex items-center space-x-2">
            <Link to="/">
              <motion.div
                className={`glass-effect px-6 py-3 rounded-full font-medium transition-all duration-300 border ${
                  isActive('/')
                    ? 'bg-gradient-primary text-white border-auspex-orange-500/50 shadow-glow'
                    : 'text-gray-300 hover:text-white border-auspex-navy-400/30 hover:border-auspex-orange-400/50'
                }`}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              >
                Home
              </motion.div>
            </Link>

            <Link to="/releases">
              <motion.div
                className={`glass-effect px-6 py-3 rounded-full font-medium transition-all duration-300 border ${
                  isActive('/releases')
                    ? 'bg-gradient-primary text-white border-auspex-orange-500/50 shadow-glow'
                    : 'text-gray-300 hover:text-white border-auspex-navy-400/30 hover:border-auspex-orange-400/50'
                }`}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              >
                Releases
              </motion.div>
            </Link>

            <Link to="/live">
              <motion.div
                className={`glass-effect px-6 py-3 rounded-full font-medium transition-all duration-300 border ${
                  isActive('/live')
                    ? 'bg-gradient-primary text-white border-auspex-orange-500/50 shadow-glow'
                    : 'text-gray-300 hover:text-white border-auspex-navy-400/30 hover:border-auspex-orange-400/50'
                }`}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              >
                Live Performances
              </motion.div>
            </Link>
          </nav>
        </div>
      </div>
    </motion.nav>
  );
};
