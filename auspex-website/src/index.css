@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  color-scheme: dark;
  --gradient-primary: linear-gradient(135deg, #ff7f00 0%, #e66a00 50%, #cc5500 100%);
  --gradient-secondary: linear-gradient(135deg, #4f63ff 0%, #2a3399 50%, #1a2066 100%);
  --gradient-accent: linear-gradient(135deg, #9f7aff 0%, #8b5fff 50%, #7a4fe6 100%);
  --gradient-navy: linear-gradient(135deg, #0f1433 0%, #080a1a 50%, #040508 100%);
  --shadow-glow: 0 0 30px rgba(255, 127, 0, 0.4);
  --shadow-glow-strong: 0 0 50px rgba(255, 127, 0, 0.6);
  --shadow-navy: 0 0 30px rgba(79, 99, 255, 0.3);
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family:
    'Poppins',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Roboto',
    'Oxygen',
    'Ubuntu',
    'Cantarell',
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: radial-gradient(ellipse at center, #0f1433 0%, #080a1a 50%, #040508 100%);
  color: white;
  overflow-x: hidden;
  line-height: 1.6;
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #080a1a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #ff7f00, #e66a00);
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #ff9933, #ff7f00);
  box-shadow: 0 0 10px rgba(255, 127, 0, 0.5);
}

/* Smooth transitions for all elements */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom utility classes */
@layer utilities {
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-secondary {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-accent {
    background: var(--gradient-accent);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .shadow-glow-strong {
    box-shadow: var(--shadow-glow-strong);
  }

  .backdrop-blur-glass {
    backdrop-filter: blur(20px) saturate(200%);
    background: linear-gradient(
      135deg,
      rgba(15, 20, 51, 0.4) 0%,
      rgba(8, 10, 26, 0.6) 50%,
      rgba(4, 5, 8, 0.8) 100%
    );
    border: 1px solid rgba(255, 127, 0, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 20px rgba(255, 127, 0, 0.1);
  }

  .glass-effect {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 127, 0, 0.05) 50%,
      rgba(79, 99, 255, 0.05) 100%
    );
    backdrop-filter: blur(15px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .ultra-glass {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 127, 0, 0.08) 30%,
      rgba(79, 99, 255, 0.08) 70%,
      rgba(255, 255, 255, 0.05) 100%
    );
    backdrop-filter: blur(25px) saturate(200%) brightness(1.1);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.3),
      inset 0 -2px 0 rgba(255, 127, 0, 0.1),
      0 0 40px rgba(255, 127, 0, 0.1);
    position: relative;
    overflow: hidden;
  }

  .ultra-glass::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s infinite;
  }

  .liquid-glass {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.12) 0%,
      rgba(255, 127, 0, 0.06) 25%,
      rgba(79, 99, 255, 0.06) 75%,
      rgba(255, 255, 255, 0.08) 100%
    );
    backdrop-filter: blur(20px) saturate(190%) hue-rotate(10deg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.25),
      0 0 20px rgba(255, 127, 0, 0.15);
    animation: liquid-flow 6s ease-in-out infinite;
  }

  .hover-lift {
    transition:
      transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      backdrop-filter 0.4s ease;
  }

  .hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.4),
      0 0 30px rgba(255, 127, 0, 0.2);
    backdrop-filter: blur(25px) saturate(220%);
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradientShift 8s ease-in-out infinite;
  }
}

/* Loading animations */
@keyframes pulse-ring {
  0% {
    transform: scale(0.33);
  }
  40%,
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(1.2);
  }
}

/* Focus styles */
button:focus,
a:focus,
input:focus,
textarea:focus {
  outline: 2px solid #ff4f00;
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background: rgba(255, 79, 0, 0.3);
  color: white;
}

/* Smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Loading spinner animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    @apply bg-background text-foreground;
  }
}
