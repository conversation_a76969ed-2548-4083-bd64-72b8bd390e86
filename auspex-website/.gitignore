# Node.js dependencies
node_modules/

# Build outputs
dist/
dist-ssr/
build/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Local development
*.local

# Testing
coverage/
.nyc_output/

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# VS Code specific
.vscode/*
!.vscode/extensions.json

# Terraform
**/.terraform/*
*.tfstate
*.tfstate.*
crash.log
crash.*.log
*.tfvars
*.tfvars.json
override.tf
override.tf.json
*_override.tf
*_override.tf.json
.terraformrc
terraform.rc
.terraform.lock.hcl
lambda.zip
