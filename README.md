# Auspex Records

A complete web platform for Auspex Records, featuring auspex-website/ for the frontend and tools/ for audio processing automation.

## 🌐 Live Sites

[auspexrecords.com](https://auspexrecords.com)

## 📁 Project Structure

```
auspex/
├── auspex-website/         # Main website application
└── tools/                 # Audio processing and automation tools
```

## 📂 Project Components

### [auspex-website/](./auspex-website/)

The main website application with React frontend and AWS infrastructure.

- See [Website Documentation](./auspex-website/README.md) for detailed setup and deployment

### [tools/](./tools/)

Audio processing and automation tools for managing music releases.

- See [Tools Documentation](./tools/README.md) for usage instructions

## 🎵 Features

- **Music Releases** - Browse and stream releases with embedded YouTube videos
- **Live Performances** - Watch recorded live performances
- **Multi-Platform Links** - Direct links to Spotify, Bandcamp, SoundCloud, etc.
- **Download Options** - Multiple audio formats (FLAC, MP3, WAV, etc.)
- **Interactive Animations** - Dynamic background with floating album covers
- **Responsive Design** - Optimized for all devices with dark theme

## 🤝 Contributing

1. Make changes in the appropriate directory (`auspex-website/` or `tools/`)
2. Test locally before deploying
3. Always test on staging before production
4. Follow the code standards outlined in each component's README

## 📄 License

All rights reserved © Auspex Records
