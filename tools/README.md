# Auspex Records Tools

Automation tools for processing and managing music releases for Auspex Records.

## [`convert_s3_codecs.py`](convert_s3_codecs.py)

Automated audio processing pipeline that converts WAV files into multiple formats (MP3, FLAC, AAC, etc.) and creates download packages for the website.

### How It Works

1. Compares S3 buckets to identify new releases
2. Downloads raw audio files from preprocessing bucket
3. Converts audio to multiple formats using FFmpeg
4. Packages releases into format-specific ZIP files
5. Uploads processed releases to public releases bucket

### Requirements

- Python 3.12+
- FFmpeg with codec support
- AWS CLI configured
- S3 buckets: `auspex-records-preprocess` and `auspex-records-releases`

### Input Format

Place WAV files in preprocessing bucket:

```
auspex-records-preprocess/
└── Artist Name - Album Title/
    ├── 01 - Track Name.wav
    ├── 02 - Another Track.wav
    └── cover.jpg (optional)
```

### Output Formats

Creates ZIP files for each format: MP3 (320k, V0), FLAC, AAC, Ogg Vorbis, ALAC, WAV, AIFF

### Setup

1. Install dependencies:

   ```bash
   pip install -r requirements.txt
   brew install ffmpeg  # or equivalent for your OS
   ```

2. Configure AWS CLI:

   ```bash
   aws configure
   ```

3. Ensure S3 buckets exist and you have appropriate permissions

### Usage

```bash
cd tools
python convert_s3_codecs.py
```

The script processes new releases by downloading, converting, packaging, and uploading to the releases bucket.

---

## [`create_youtube_video.py`](create_youtube_video.py)

Creates 4K YouTube videos from audio files and static images using FFmpeg.

### Requirements

- Python 3.6+
- FFmpeg with H.264 and AAC support

### Usage

```bash
python create_youtube_video.py \
    --audio-folder "path/to/audio" \
    --image "path/to/cover.jpg" \
    --output-folder "videos"
```

### Supported Formats

- **Audio**: MP3, WAV, FLAC, M4A, OGG, AAC
- **Images**: JPG, PNG, BMP (recommend 4K resolution)

### Options

- `--audio-folder`: Path to audio files (required)
- `--image`: Default image for all videos (required)
- `--output-folder`: Output directory (optional)
- `--image-folder`: Track-specific images (optional)

Creates 4K MP4 videos (3840x2160) with H.264 video and AAC audio. Processing time is approximately 2-5 minutes per minute of audio.
